import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";

export const deviceTimerService = {
  // Start a new timer
  createTimer: async ({ startTime, durationSet }) => {
    try {
      // durationSet should be in seconds when sending to the API
      const res = await apiClient.post("/device_timer", {
        startTime: startTime,
        durationSet: durationSet, // Already in seconds
      });
      // console.log('Create timer ===', res);
      return {
        success: true,
        message: res?.msg || "Device usage record created successfully",
        data: res?.data || null,
        sessionId: res?.data?.sessions?.[0]?.id || null, // Return the session ID if available
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error creating timer.",
      };
    }
  },

  // Legacy method for backward compatibility
  logTimerData: async ({ duration, startTime }) => {
    try {
      // Use the new createTimer method with the appropriate parameters
      const res = await deviceTimerService.createTimer({
        startTime: startTime,
        durationSet: duration,
      });

      return {
        success: res.success,
        message: res.message,
        sessionId: res.sessionId,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error recording timer data.",
      };
    }
  },

  // Update a timer session
  updateTimerSession: async ({ sessionId, durationConsumed }) => {
    try {
      // durationConsumed should be in seconds when sending to the API
      const res = await apiClient.put(`/device_timer/${sessionId}`, {
        durationConsumed: durationConsumed, // Already in seconds
      });

      return {
        success: true,
        message: res?.msg || "Device usage session updated successfully",
        data: res?.data || null,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error updating timer session.",
      };
    }
  },

  // End a timer session
  endTimerSession: async ({ sessionId, durationConsumed, endTime }) => {
    try {
      const res = await apiClient.put(`/device_timer/end/${sessionId}`, {
        durationConsumed: durationConsumed,
        endTime: endTime,
      });

      return {
        success: true,
        message: res?.msg || "Device usage session ended and summary updated.",
        data: res?.data || null,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error ending timer session.",
      };
    }
  },

  // Legacy method for backward compatibility
  endTimer: async ({ timerId, duration, endTime }) => {
    try {
      // Use the new endTimerSession method with the appropriate parameters
      const res = await deviceTimerService.endTimerSession({
        sessionId: timerId,
        durationConsumed: duration,
        endTime: endTime,
      });

      return {
        success: res.success,
        message: res.message,
        data: res.data || null,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error ending timer.",
      };
    }
  },

  // Get timer history for a specific date
  getTimerHistoryByDate: async (date) => {
    try {
      const formattedDate = date.toISOString().split("T")[0];
      const res = await apiClient.get(`/device_timer?date=${formattedDate}`);

      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching timer history for date.",
        data: [],
      };
    }
  },

  // Legacy method for backward compatibility
  getAllTimerHistory: async () => {
    try {
      // Get today's timer history
      const today = new Date();
      const res = await deviceTimerService.getTimerHistoryByDate(today);
      console.log("Timer history for today:", res.data);

      return {
        success: res.success,
        data: res.data || [],
        error: res.error,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error fetching timer history.",
        data: [],
      };
    }
  },

  // Helper function to format date without timezone conversion
  formatDateTimeForAPI: (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.00`;
  },

  // Add a past timer entry
  addMoreTimer: async ({ startTime, durationConsumed }) => {
    try {
      console.log("Adding more timer - Service received:", startTime, durationConsumed);

      // Format the startTime without timezone conversion
      const formattedStartTime = deviceTimerService.formatDateTimeForAPI(startTime);
      console.log("Adding more timer - Service formatted startTime:", formattedStartTime);

      // durationConsumed should be in seconds when sending to the API
      const res = await apiClient.post("/device_timer/add_more", {
        startTime: formattedStartTime,
        durationConsumed: durationConsumed, // Already in seconds
      });

      return {
        success: true,
        message:
          res?.msg || "Device usage session created and summary updated.",
        data: res?.data || null,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error adding past timer entry.",
      };
    }
  },

  // Update a past timer entry
  updateAddMoreTimer: async ({ id, startTime, durationConsumed }) => {
    try {
      // Format the startTime without timezone conversion
      const formattedStartTime = deviceTimerService.formatDateTimeForAPI(startTime);
      console.log("Updating timer - Service formatted startTime:", formattedStartTime);

      // durationConsumed should be in seconds when sending to the API
      const res = await apiClient.put(`/device_timer/add_more/${id}`, {
        startTime: startTime,
        durationConsumed: durationConsumed, // Already in seconds
      });

      return {
        success: true,
        message:
          res?.msg || "Device usage session updated and summary adjusted.",
        data: res?.data || null,
      };
    } catch (error) {
      // console.log(error);

      return {
        success: false,
        error: error?.message || "Error updating timer entry.",
      };
    }
  },

  // Get recent timer data
  getLastLoggedData: async () => {
    try {
      const res = await apiClient.get("/device_timers");

      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent timer logged data.",
      };
    }
  },

  getTimerGraphRecords: async (filter) => {
    try {
      const res = await apiClient.get(
        `/device_timer_analytics?filter=${filter}`
      );

      let timerGraphRecords = [];
      let timePeriod = null;

      if (filter === "weekly") {
        timerGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: getDayName(item.period).substring(0, 1),
          };
        });

        timePeriod = getTimePeriod(
          res.data[0].period,
          res.data[res.data.length - 1].period
        );
      } else if (filter === "monthly") {
        timerGraphRecords = res.data.map((item) => {
          const weekNumber = item.period.match(/week_(\d+)/);
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: `W${weekNumber[1]}`,
          };
        });

        timePeriod = getTimePeriod(
          res.data[0].startDate,
          res.data[res.data.length - 1].endDate
        );
      } else if (filter === "half_yearly") {
        timerGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          res.data[0].year !== res.data[res.data.length - 1].year
            ? `${res.data[0].period.substring(0, 3)} ${res.data[0].year
            } - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[res.data.length - 1].year
            }`
            : `${res.data[0].period.substring(0, 3)} - ${res.data[
              res.data.length - 1
            ].period.substring(0, 3)} ${res.data[0].year}`;
      } else if (filter === "yearly") {
        timerGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          res.data[0].year !== res.data[res.data.length - 1].year
            ? `${res.data[0].period.substring(0, 3)} ${res.data[0].year
            } - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[res.data.length - 1].year
            }`
            : `${res.data[0].period.substring(0, 3)} - ${res.data[
              res.data.length - 1
            ].period.substring(0, 3)} ${res.data[0].year}`;
      }

      return {
        success: true,
        data: {
          graphData: timerGraphRecords,
          timePeriod: timePeriod,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
};
