import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, AppState, Platform } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { AuthProvider } from './context/AuthContext';
import { AppContent } from './navigations/AppContent';
import { FontLoad } from 'constants/fontLoad';
import { Colors } from 'constants/theme/colors';
import { useKeepAwake } from 'expo-keep-awake';
import { CustomLoader } from 'components/CustomAction';
import NetInfo from '@react-native-community/netinfo';
import * as Notifications from "expo-notifications";
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { linkingConfig } from 'navigations/LinkingConfig';
import * as Linking from 'expo-linking';
import { getIsFirstCall, insertSampleData, readSampleData, setIsFirstCall } from 'services/health/healthDataService';
import { readAppleHealthData } from 'services/health/appleHealthDataService';
import Toast from 'react-native-toast-message';
import { navigationRef } from './navigations/RootNavigation';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;
// console.log("App.js apiUrl : ", apiUrl);

import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { registerBackgroundTask } from 'services/backgroundService';
import useHealthPermissionStore from 'store/healthPermissionStore';
import useAppleHealthPermissionStore from 'store/appleHealthPermissionStore';
import checkWithinLastHour from 'utils/checkWithinLastHour';
import useGlobalErrorStore from 'store/globalErrorStore';
import { useHealthData } from 'hooks/useHealthData';
import { KeyboardProvider } from 'react-native-keyboard-controller';

// Background task for health data is commented out
// TaskManager.defineTask('background-fetch', async () => {
//   const now = Date.now();

//   console.log(`Got background fetch call at date: ${new Date(now).toLocaleTimeString()}`);

//   if (Platform.OS == "android") {
//     await readSampleData(1);
//   }

//   return BackgroundFetch.BackgroundFetchResult.NewData;
// });

// Background timer task is defined in backgroundTimerService.js

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

SplashScreen.preventAutoHideAsync();

export default function App() {

  const fontsLoaded = FontLoad();
  const lastErrorTimeRef = useRef(null);

  const [isServerConnected, setIsServerConnected] = useState(false);
  const [isInternetConnected, setIsInternetConnected] = useState(true);
  const [loading, setLoading] = useState(true);

  const { hasBackgroundPermission } = useHealthPermissionStore(state => state);
  const { hasAppleHealthPermission } = useAppleHealthPermissionStore(state => state);
  const { setHealthDataError } = useGlobalErrorStore(state => state);

  const healthDataHook = useHealthData();

  // Initialize the hook with try-catch for error handling
  let healthData = {};
  try {
    healthData = healthDataHook();
  } catch (error) {
    console.error('Error initializing health data hook:', error);
  }
  const {
    hasApplePermissions = false,
    hasPermissions = false, // Direct access to the internal permission state
    requestPermission = async () => ({ granted: false }),
    removePermission = async () => false,
    fetchAllAppleHealthData = async () => ({ error: 'Function not available' }),
  } = healthData;

  useKeepAwake();

  // Check for internet connection
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsInternetConnected(state.isConnected);
    });

    return () => unsubscribe();
  }, []);

  // Check for server connection
  const checkServerConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${apiUrl}/health`);
      // console.log('checkServerConnection response:', response);
      setIsServerConnected(response.ok);
    } catch (error) {
      // console.error('Server connection failed:', error);
      setIsServerConnected(false);
    }
    setLoading(false);
  };



  useEffect(() => {
    let intervalId;
    let isFetching = false;

    const fetchAndroidData = async (count) => {
      if (isFetching) return;

      isFetching = true;

      const res = await readSampleData(count, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
      if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
        lastErrorTimeRef.current = new Date();
        setHealthDataError(res.error);
      }

      isFetching = false;
    };

    const fetchAppleHealthData = async (count) => {
      if (isFetching) return;

      isFetching = true;

      try {
        // Only fetch data if we have permission according to the store
        if (Platform.OS === 'ios' && hasApplePermissions) {
          console.log('Fetching Apple Health data...');
          const res = await readAppleHealthData(count, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);

          // Log the fetched data to console
          console.log('Apple Health Data:', res);

          if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
            lastErrorTimeRef.current = new Date();
            setHealthDataError(res.error);
          }
        } else if (Platform.OS === 'ios') {
          console.log('Apple HealthKit permissions not granted according to store');
        }
      } catch (error) {
        console.error('Error in fetchAppleHealthData:', error);
      } finally {
        isFetching = false;
      }
    };

    if (Platform.OS === 'android') {
      (async () => {
        const isFirstCall = await getIsFirstCall();

        if (isFirstCall) {
          await setIsFirstCall();
        }

        await fetchAndroidData(isFirstCall ? 30 : 7);

        intervalId = setInterval(() => {
          fetchAndroidData(1);
        }, 10 * 60 * 1000);

      })();
    } else if (Platform.OS === 'ios') {
      (async () => {
        // Fetch Apple Health data when app opens
        await fetchAppleHealthData(7);

        // Set up interval to fetch data periodically
        intervalId = setInterval(() => {
          fetchAppleHealthData(1);
        }, 10 * 60 * 1000);
      })();
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [hasAppleHealthPermission]);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        if (Platform.OS === "android") {
          readSampleData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
        } else if (Platform.OS === "ios" && hasAppleHealthPermission) {
          console.log('Initial fetch: Fetching Apple Health data...');
          try {
            const res = await readAppleHealthData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
            console.log('Initial Apple Health Data:', res);
          } catch (err) {
            console.error('Error fetching initial Apple Health data:', err);
          }
        } else if (Platform.OS === "ios") {
          console.log('Initial fetch: Apple HealthKit permissions not granted according to store');
        }
      } catch (error) {
        console.error('Error in fetchInitialData:', error);
      }
    };

    fetchInitialData();
  }, [hasAppleHealthPermission]);


  // useEffect(() => {
  //   if (Platform.OS == "android" && hasBackgroundPermission) {
  //     registerBackgroundTask();
  //   }
  // }, [hasBackgroundPermission]);


  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const subscription = AppState.addEventListener("change", async (nextAppState) => {
      if (
        appState.current.match(/inactive|background|unknown|extension/) &&
        nextAppState === "active"
      ) {
        try {
          if (Platform.OS === "android") {
            const res = await readSampleData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
            if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
              lastErrorTimeRef.current = new Date();
              setHealthDataError(res.error);
            }
          } else if (Platform.OS === "ios" && hasAppleHealthPermission) {
            console.log('Foreground: Fetching Apple Health data...');
            try {
              const res = await readAppleHealthData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
              console.log('Apple Health Data (foreground):', res);

              if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
                lastErrorTimeRef.current = new Date();
                setHealthDataError(res.error);
              }
            } catch (err) {
              console.error('Error fetching foreground Apple Health data:', err);
            }
          } else if (Platform.OS === "ios") {
            console.log('Foreground: Apple HealthKit permissions not granted according to store');
          }
        } catch (error) {
          console.error('Error in AppState change handler:', error);
        }
      }

      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [hasAppleHealthPermission]);

  // useEffect(() => {
  //   TaskManager.isTaskRegisteredAsync("background-fetch").then((isRegistered) => {
  //     console.log(`Is task registered? ${isRegistered}`);
  //   });
  // }, []);

  useEffect(() => {
    // Set up notification listeners
    const notificationListener = Notifications.addNotificationReceivedListener(() => {
      // We're not using the notification data, just setting up the listener
    });

    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      const redirectURL = response.notification.request.content.data.url;
      Linking.openURL(redirectURL);
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, []);

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
      checkServerConnection();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded || loading) {
    return (
      <View style={styles.container}>
        <CustomLoader />
      </View>
    );
  }

  if (!isInternetConnected) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No Internet Connection</Text>
        <TouchableOpacity onPress={() => checkServerConnection()} style={styles.retryButton}>
          <Text style={styles.retryText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!isServerConnected) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Server Not Responding</Text>
        <TouchableOpacity onPress={() => checkServerConnection()} style={styles.retryButton}>
          <Text style={styles.retryText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linkingConfig}
      fallback={
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <CustomLoader />
        </View>
      }>
      <KeyboardProvider>
        <SafeAreaProvider>
          <AuthProvider>
            <SafeAreaView
              style={{
                flex: 1,
                backgroundColor: Colors.primaryGreen,
              }}
              edges={["bottom"]}
            >
              <StatusBar translucent backgroundColor='transparent' />
              <AppContent />
              <Toast />
            </SafeAreaView>
          </AuthProvider>
        </SafeAreaProvider>
      </KeyboardProvider>
    </NavigationContainer>

  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontFamily: 'Exo_700Bold',
    fontSize: 20,
    color: Colors.primaryGreen,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.primaryGreen,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  retryText: {
    fontSize: 16,
    color: '#fff',
    fontFamily: 'Exo_600SemiBold',
  },
});
