import { Image, StyleSheet, Text, View } from "react-native";
import React, { useState } from "react";
import { CustomButton } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import { getYouTubeThumbnail } from "utils/youtube";
import YouTubePlayerModal from "components/CustomVideo/YouTubePlayerModal";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";

const VideoOverviewCard = ({ video }) => {
  // const [showVideo, setShowVideo] = useState(false);
  // const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  return (
    <>
      <View style={{ borderRadius: 25, overflow: "hidden" }}>
        <Image
          source={{ uri: getYouTubeThumbnail(video.videoUrl) }}
          style={styles.workoutBgImage}
        />
        <View style={styles.workoutContainer}>
          <View style={{ flex: 1, alignItems: "flex-start", gap: 4 }}>
            <Text
              style={{
                fontSize: 10,
                fontFamily: ThemeFonts.Exo_400,
                backgroundColor: Colors.primaryGreen,
                color: Colors.white,
                paddingHorizontal: 8,
                borderRadius: 10,
                paddingVertical: 2,
              }}
            >
              Recommended
            </Text>
            <Text style={[styles.subHeading, { fontSize: 19 }]} numberOfLines={1}>
              {video.title}
            </Text>
          </View>
          <CustomButton
            title="Start"
            onPress={() =>
              navigation.navigate("Video Details", {
                videoId: video?.id,
                title: video?.title,
                description: video?.description,
                videoURL: video?.videoUrl,
              })
            }
            style={{ width: 80 }}
          />
        </View>
      </View>
    </>
  );
};

export default VideoOverviewCard;

const styles = StyleSheet.create({
  workoutBgImage: {
    width: "100%",
    aspectRatio: 16 / 10,
    borderRadius: 25,
  },
  workoutContainer: {
    position: "absolute",
    right: 12,
    left: 12,
    bottom: 12,
    backgroundColor: Colors.veryLightGreen,
    padding: 16,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "flex-end",
  },
  recommendedTag: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_400,
    backgroundColor: Colors.primaryGreen,
    color: Colors.white,
    paddingHorizontal: 8,
    borderRadius: 10,
    paddingVertical: 2,
  },
  subHeading: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
});
